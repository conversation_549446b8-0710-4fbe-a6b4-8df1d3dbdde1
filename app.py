from flask import Flask, request, jsonify
from flask_cors import CORS
from werkzeug.security import generate_password_hash, check_password_hash
import psycopg2
from psycopg2.extras import RealDictCursor
import os
from contextlib import contextmanager

app = Flask(__name__)
CORS(app)

# Database connection configuration
DB_CONFIG = {
    'dbname': os.environ.get('DB_NAME', 'TaskManager'),
    'user': os.environ.get('DB_USER', 'postgres'),
    'password': os.environ.get('DB_PASSWORD', 'root'),
    'host': os.environ.get('DB_HOST', 'localhost'),
    'port': os.environ.get('DB_PORT', '5432')
}

# Context manager for database connections
@contextmanager
def get_db_connection():
    conn = None
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        yield conn
    except psycopg2.Error as e:
        print(f"Database error: {e}")
        if conn:
            conn.rollback()
        raise
    finally:
        if conn:
            conn.close()

@app.route("/")
def home():
    return jsonify({"message": "Task Manager API is running!"}), 200

# ---------------------------
# User Registration
# ---------------------------
@app.route("/register", methods=["POST"])
def register():
    try:
        data = request.get_json()
        username = data.get("username")
        email = data.get("email")
        password = data.get("password")

        if not username or not email or not password:
            return jsonify({"error": "Missing fields"}), 400

        # Validate email format (basic validation)
        if '@' not in email or '.' not in email:
            return jsonify({"error": "Invalid email format"}), 400

        # Validate password strength (minimum 8 characters)
        if len(password) < 8:
            return jsonify({"error": "Password must be at least 8 characters long"}), 400

        hashed_password = generate_password_hash(password)

        with get_db_connection() as conn:
            with conn.cursor() as cur:
                # Check if user already exists
                cur.execute("SELECT * FROM users WHERE username = %s OR email = %s", (username, email))
                if cur.fetchone():
                    return jsonify({"error": "User already exists"}), 400

                # Insert new user
                cur.execute(
                    "INSERT INTO users (username, email, password_hash) VALUES (%s, %s, %s) RETURNING id",
                    (username, email, hashed_password)
                )
                user_id = cur.fetchone()[0]
                conn.commit()
                
        return jsonify({"message": "User registered successfully", "user_id": user_id}), 201
    
    except psycopg2.Error as e:
        return jsonify({"error": "Database error occurred"}), 500
    except Exception as e:
        return jsonify({"error": "An error occurred"}), 500

# ---------------------------
# User Login
# ---------------------------
@app.route("/login", methods=["POST"])
def login():
    try:
        data = request.get_json()
        username = data.get("username")
        password = data.get("password")

        if not username or not password:
            return jsonify({"error": "Missing username or password"}), 400

        with get_db_connection() as conn:
            with conn.cursor() as cur:
                cur.execute("SELECT id, username, password_hash FROM users WHERE username = %s", (username,))
                user = cur.fetchone()

        if user is None:
            return jsonify({"error": "Invalid username or password"}), 401

        user_id, user_name, password_hash = user

        if check_password_hash(password_hash, password):
            # In production, you should return a JWT token here
            return jsonify({
                "message": "Login successful", 
                "user_id": user_id,
                "username": user_name
            }), 200
        else:
            return jsonify({"error": "Invalid username or password"}), 401
    
    except Exception as e:
        return jsonify({"error": "An error occurred"}), 500

# ---------------------------
# Get Tasks for Specific User
# ---------------------------
@app.route("/tasks", methods=["GET"])
def get_tasks():
    try:
        user_id = request.args.get("user_id")
        if not user_id:
            return jsonify({"error": "Missing user_id"}), 400

        # Validate user_id is a number
        try:
            user_id = int(user_id)
        except ValueError:
            return jsonify({"error": "Invalid user_id"}), 400

        with get_db_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                # Verify user exists
                cur.execute("SELECT id FROM users WHERE id = %s", (user_id,))
                if not cur.fetchone():
                    return jsonify({"error": "User not found"}), 404

                # Get tasks
                cur.execute(
                    "SELECT id, title, description, completed, created_at FROM tasks WHERE user_id = %s ORDER BY created_at DESC", 
                    (user_id,)
                )
                tasks = cur.fetchall()

        return jsonify(tasks), 200
    
    except Exception as e:
        return jsonify({"error": "An error occurred"}), 500

# ---------------------------
# Create New Task
# ---------------------------
@app.route("/tasks", methods=["POST"])
def create_task():
    try:
        data = request.get_json()
        title = data.get("title")
        description = data.get("description", "")  # Default to empty string
        user_id = data.get("user_id")

        if not title or not user_id:
            return jsonify({"error": "Missing required fields"}), 400

        # Validate user_id
        try:
            user_id = int(user_id)
        except ValueError:
            return jsonify({"error": "Invalid user_id"}), 400

        with get_db_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                # Verify user exists
                cur.execute("SELECT id FROM users WHERE id = %s", (user_id,))
                if not cur.fetchone():
                    return jsonify({"error": "User not found"}), 404

                # Insert task
                cur.execute(
                    "INSERT INTO tasks (title, description, completed, user_id) VALUES (%s, %s, %s, %s) RETURNING *",
                    (title, description, False, user_id)
                )
                new_task = cur.fetchone()
                conn.commit()

        return jsonify({"message": "Task added successfully", "task": new_task}), 201
    
    except Exception as e:
        return jsonify({"error": "An error occurred"}), 500

# ---------------------------
# Update Task
# ---------------------------
@app.route("/tasks/<int:task_id>", methods=["PATCH"])
def update_task(task_id):
    try:
        data = request.get_json()
        user_id = data.get("user_id")  # Should verify ownership
        
        if not user_id:
            return jsonify({"error": "Missing user_id"}), 400

        # Get update fields
        title = data.get("title")
        description = data.get("description")
        completed = data.get("completed")

        # Build dynamic update query
        update_fields = []
        params = []
        
        if title is not None:
            update_fields.append("title = %s")
            params.append(title)
        if description is not None:
            update_fields.append("description = %s")
            params.append(description)
        if completed is not None:
            update_fields.append("completed = %s")
            params.append(completed)
        
        if not update_fields:
            return jsonify({"error": "No fields to update"}), 400

        params.extend([task_id, user_id])
        
        with get_db_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                # Update task only if it belongs to the user
                query = f"""
                    UPDATE tasks 
                    SET {', '.join(update_fields)}
                    WHERE id = %s AND user_id = %s
                    RETURNING *
                """
                cur.execute(query, params)
                updated_task = cur.fetchone()
                
                if not updated_task:
                    return jsonify({"error": "Task not found or unauthorized"}), 404
                
                conn.commit()

        return jsonify({"message": "Task updated successfully", "task": updated_task}), 200
    
    except Exception as e:
        return jsonify({"error": "An error occurred"}), 500

# ---------------------------
# Delete Task
# ---------------------------
@app.route("/tasks/<int:task_id>", methods=["DELETE"])
def delete_task(task_id):
    try:
        # Get user_id from query params or request body
        user_id = request.args.get("user_id") or request.get_json().get("user_id")
        
        if not user_id:
            return jsonify({"error": "Missing user_id"}), 400

        try:
            user_id = int(user_id)
        except ValueError:
            return jsonify({"error": "Invalid user_id"}), 400

        with get_db_connection() as conn:
            with conn.cursor() as cur:
                # Delete only if task belongs to user
                cur.execute(
                    "DELETE FROM tasks WHERE id = %s AND user_id = %s RETURNING id", 
                    (task_id, user_id)
                )
                deleted = cur.fetchone()
                
                if not deleted:
                    return jsonify({"error": "Task not found or unauthorized"}), 404
                
                conn.commit()

        return jsonify({"message": "Task deleted successfully"}), 200
    
    except Exception as e:
        return jsonify({"error": "An error occurred"}), 500

# ---------------------------
# Error Handlers
# ---------------------------
@app.errorhandler(404)
def not_found(error):
    return jsonify({"error": "Resource not found"}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({"error": "Internal server error"}), 500

# ---------------------------
# Database Schema (for reference)
# ---------------------------
"""
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE tasks (
    id SERIAL PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    completed BOOLEAN DEFAULT FALSE,
    user_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
"""

# ---------------------------
# Start the App
# ---------------------------
if __name__ == "__main__":
    app.run(debug=True)